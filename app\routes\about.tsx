import type { Route } from "./+types/about";

// biome-ignore lint/correctness/noEmptyPattern: <explanation>
export function meta({}: Route.MetaArgs) {
   return [
      { title: "关于我们 - bknd React Router App" },
      { name: "description", content: "了解更多关于我们的信息" },
   ];
}

export default function About() {
   return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
         <div className="container mx-auto px-4 py-16">
            <div className="max-w-4xl mx-auto">
               <header className="text-center mb-12">
                  <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                     关于我们
                  </h1>
                  <p className="text-xl text-gray-600 dark:text-gray-300">
                     欢迎了解我们的故事和使命
                  </p>
               </header>

               <div className="grid md:grid-cols-2 gap-12 mb-16">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
                     <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                        我们的使命
                     </h2>
                     <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        我们致力于创建高质量的Web应用程序，为用户提供出色的体验。
                        通过使用最新的技术栈，包括React Router 7和现代化的开发工具，
                        我们构建快速、可靠且用户友好的应用程序。
                     </p>
                  </div>

                  <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
                     <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                        技术栈
                     </h2>
                     <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                        <li>• React Router 7</li>
                        <li>• TypeScript</li>
                        <li>• Tailwind CSS</li>
                        <li>• Vite</li>
                        <li>• bknd 后端服务</li>
                     </ul>
                  </div>
               </div>

               <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg mb-12">
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                     联系我们
                  </h2>
                  <div className="grid md:grid-cols-3 gap-6">
                     <div className="text-center">
                        <div className="text-3xl mb-2">📧</div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">邮箱</h3>
                        <p className="text-gray-600 dark:text-gray-300"><EMAIL></p>
                     </div>
                     <div className="text-center">
                        <div className="text-3xl mb-2">📱</div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">电话</h3>
                        <p className="text-gray-600 dark:text-gray-300">+86 123 4567 8900</p>
                     </div>
                     <div className="text-center">
                        <div className="text-3xl mb-2">📍</div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">地址</h3>
                        <p className="text-gray-600 dark:text-gray-300">北京市朝阳区</p>
                     </div>
                  </div>
               </div>

               <div className="text-center">
                  <a 
                     href="/" 
                     className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
                  >
                     ← 返回首页
                  </a>
               </div>
            </div>
         </div>
      </div>
   );
}
