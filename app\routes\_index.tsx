import type { Route } from "./+types/_index";
import {
   type ActionFunctionArgs,
   type LoaderFunctionArgs,
   useFetcher,
   useLoaderData,
} from "react-router";
import { getApi } from "~/bknd";

export function meta({}: Route.MetaArgs) {
   return [
      { title: "New bknd React Router App" },
      { name: "description", content: "Welcome to bknd & React Router!" },
   ];
}

export const loader = async (args: LoaderFunctionArgs) => {
   const api = await getApi(args, { verify: true });
   const limit = 5;
   const { data: todos, body: { meta } } = await api.data.readMany("todos", {
      limit,
      sort: "-id",
   });
   return { todos: todos.reverse(), total: meta.total, limit, user: api.getUser() };
};

// 优化的action处理
type TodoAction = "add" | "update" | "delete";
const AUTHENTICATED_ACTIONS: TodoAction[] = ["add", "update"];

const validateTodoData = (action: TodoAction, formData: FormData) => {
   switch (action) {
      case "add": {
         const title = formData.get("title") as string;
         if (!title?.trim()) throw new Response("标题不能为空", { status: 400 });
         if (title.length > 100) throw new Response("标题过长", { status: 400 });
         return { title: title.trim() };
      }
      case "update": {
         const id = Number(formData.get("id"));
         const done = formData.get("done") === "on";
         if (!id || id <= 0) throw new Response("无效ID", { status: 400 });
         return { id, done };
      }
      case "delete": {
         const id = Number(formData.get("id"));
         if (!id || id <= 0) throw new Response("无效ID", { status: 400 });
         return { id };
      }
      default:
         throw new Response("无效操作", { status: 400 });
   }
};

export const action = async (args: ActionFunctionArgs) => {
   try {
      const formData = await args.request.formData();
      const action = formData.get("action") as TodoAction;
      if (!action) throw new Response("缺少操作类型", { status: 400 });

      const validatedData = validateTodoData(action, formData);
      const needsAuth = AUTHENTICATED_ACTIONS.includes(action);
      const api = needsAuth ? await getApi(args, { verify: true }) : await getApi();

      switch (action) {
         case "add": {
            const { title } = validatedData as { title: string };
            await api.data.createOne("todos", { title, done: false });
            break;
         }
         case "update": {
            const { id, done } = validatedData as { id: number; done: boolean };
            await api.data.updateOne("todos", id, { done });
            break;
         }
         case "delete": {
            const { id } = validatedData as { id: number };
            await api.data.deleteOne("todos", id);
            break;
         }
      }
      return null;
   } catch (error) {
      if (error instanceof Response) return error;
      if (error instanceof Error && error.message.includes("auth")) {
         throw new Response("需要登录", { status: 401 });
      }
      console.error("Action error:", error);
      throw new Response("操作失败", { status: 500 });
   }
};

// 简化的组件
function TodoItem({ todo, user, fetcher }: { todo: any; user: any; fetcher: any }) {
   return (
      <div className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
         <fetcher.Form method="post" className="flex items-center">
            <input type="hidden" name="action" value="update" />
            <input type="hidden" name="id" value={String(todo.id)} />
            <input type="hidden" name="done" value={todo.done ? "off" : "on"} />
            <input
               type="checkbox"
               checked={todo.done}
               disabled={!user}
               onChange={(e) => fetcher.submit(e.currentTarget.form)}
               className="w-4 h-4 text-blue-600 disabled:opacity-50"
               title={!user ? "需要登录才能更新" : ""}
            />
         </fetcher.Form>
         
         <span className={`flex-1 ${todo.done ? 'line-through text-gray-500' : 'text-gray-900 dark:text-gray-100'} ${!user ? 'opacity-50' : ''}`}>
            {todo.title}
         </span>
         
         <fetcher.Form method="post">
            <input type="hidden" name="action" value="delete" />
            <input type="hidden" name="id" value={String(todo.id)} />
            <button
               type="submit"
               className="text-red-500 hover:text-red-700 p-1 rounded transition-colors"
               title="删除"
            >
               ❌
            </button>
         </fetcher.Form>
      </div>
   );
}

function AddTodoForm({ user, fetcher }: { user: any; fetcher: any }) {
   return (
      <fetcher.Form method="post" className="flex gap-2">
         <input type="hidden" name="action" value="add" />
         <input
            type="text"
            name="title"
            placeholder={user ? "添加新的待办事项..." : "需要登录才能添加"}
            disabled={!user}
            maxLength={100}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-transparent"
         />
         <button
            type="submit"
            disabled={!user}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={!user ? "需要登录才能添加" : ""}
         >
            ➕ 添加
         </button>
      </fetcher.Form>
   );
}

export default function Index() {
   const { todos, total, limit, user } = useLoaderData<typeof loader>();
   const fetcher = useFetcher();

   return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
         <div className="max-w-2xl mx-auto px-4">
            {/* Header */}
            <div className="text-center mb-8 space-y-6">
               <img src="/bknd.svg" alt="bknd" className="w-48 mx-auto dark:invert" />
               <div className="h-[100px]">
                  <img
                     src="/logo-light.svg"
                     alt="React Router"
                     className="w-full max-w-sm mx-auto dark:hidden"
                  />
                  <img
                     src="/logo-dark.svg"
                     alt="React Router"
                     className="hidden w-full max-w-sm mx-auto dark:block"
                  />
               </div>
            </div>

            {/* Main Content */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-6">
               <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                     待办事项 ({total})
                  </h2>
                  {!user && (
                     <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3 text-amber-800 dark:text-amber-200">
                        💡 需要登录才能添加或更新待办事项
                     </div>
                  )}
               </div>

               {/* Todo List */}
               <div className="space-y-3 mb-6">
                  {total > limit && (
                     <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 text-blue-800 dark:text-blue-200 text-center">
                        还有 {total - limit} 个待办事项未显示
                     </div>
                  )}
                  
                  {todos.map((todo: any) => (
                     <TodoItem key={String(todo.id)} todo={todo} user={user} fetcher={fetcher} />
                  ))}
               </div>

               {/* Add New Todo */}
               <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <AddTodoForm user={user} fetcher={fetcher} />
               </div>
            </div>

            {/* Navigation & User Info */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
               <div className="flex justify-center gap-6 mb-4">
                  <a href="/about" className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                     关于我们 →
                  </a>
                  <a href="/admin" className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                     管理后台 →
                  </a>
               </div>
               
               <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  {user ? (
                     <div className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                           👤
                        </div>
                        <span>已登录: <strong className="text-gray-900 dark:text-white">{user.email}</strong></span>
                     </div>
                  ) : (
                     <a 
                        href="/admin/auth/login"
                        className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                     >
                        🔑 登录
                     </a>
                  )}
               </div>
            </div>
         </div>
      </div>
   );
}
