import type { Route } from "./+types/_index";
import {
   type ActionFunctionArgs,
   type LoaderFunctionArgs,
   useFetcher,
   useLoaderData,
} from "react-router";
import { getApi } from "~/bknd";
import {
   Card,
   List,
   Checkbox,
   Input,
   Button,
   Space,
   Typography,
   Alert,
   Divider,
   Avatar,
   Form
} from "antd";
import {
   PlusOutlined,
   DeleteOutlined,
   UserOutlined,
   LoginOutlined
} from "@ant-design/icons";

const { Title, Text } = Typography;

// biome-ignore lint/correctness/noEmptyPattern: <explanation>
export function meta({}: Route.MetaArgs) {
   return [
      { title: "New bknd React Router App" },
      { name: "description", content: "Welcome to bknd & React Router!" },
   ];
}

export const loader = async (args: LoaderFunctionArgs) => {
   const api = await getApi(args, { verify: true });

   const limit = 5;
   const {
      data: todos,
      body: { meta },
   } = await api.data.readMany("todos", {
      limit,
      sort: "-id",
   });

   return { todos: todos.reverse(), total: meta.total, limit, user: api.getUser() };
};

export default function Index() {
   const { todos, total, limit, user } = useLoaderData<typeof loader>();
   const fetcher = useFetcher();

   return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
         <div className="max-w-4xl mx-auto px-4">
            {/* Header */}
            <div className="text-center mb-8">
               <Space direction="vertical" size="large" className="w-full">
                  <img src="/bknd.svg" alt="bknd" className="block w-48 mx-auto dark:invert" />
                  <div className="h-[120px]">
                     <img
                        src="/logo-light.svg"
                        alt="React Router"
                        className="block w-full max-w-md mx-auto dark:hidden"
                     />
                     <img
                        src="/logo-dark.svg"
                        alt="React Router"
                        className="hidden w-full max-w-md mx-auto dark:block"
                     />
                  </div>
               </Space>
            </div>
            {/* Main Content */}
            <Card className="mb-6">
               <Space direction="vertical" size="middle" className="w-full">
                  <div className="text-center">
                     <Title level={3}>待办事项 ({total})</Title>
                     {!user && (
                        <Alert
                           message="需要登录才能添加或更新待办事项"
                           type="warning"
                           showIcon
                           className="mb-4"
                        />
                     )}
                  </div>

                  {/* Todo List */}
                  <div className="space-y-2">
                     {total > limit && (
                        <Alert
                           message={`还有 ${total - limit} 个待办事项未显示`}
                           type="info"
                           className="mb-4"
                        />
                     )}

                     <List
                        dataSource={todos}
                        renderItem={(todo) => (
                           <List.Item
                              key={String(todo.id)}
                              actions={[
                                 <fetcher.Form method="post" key="delete">
                                    <input type="hidden" name="action" value="delete" />
                                    <input type="hidden" name="id" value={String(todo.id)} />
                                    <Button
                                       type="text"
                                       danger
                                       icon={<DeleteOutlined />}
                                       size="small"
                                       htmlType="submit"
                                       title="删除"
                                    />
                                 </fetcher.Form>
                              ]}
                           >
                              <List.Item.Meta
                                 avatar={
                                    <fetcher.Form method="post" data-todo-id={String(todo.id)}>
                                       <input type="hidden" name="action" value="update" />
                                       <input type="hidden" name="id" value={String(todo.id)} />
                                       <input type="hidden" name="done" value={todo.done ? "off" : "on"} />
                                       <Checkbox
                                          checked={todo.done}
                                          disabled={!user}
                                          onChange={() => {
                                             if (user) {
                                                const form = document.querySelector(`form[data-todo-id="${todo.id}"]`) as HTMLFormElement;
                                                if (form) fetcher.submit(form);
                                             }
                                          }}
                                       />
                                    </fetcher.Form>
                                 }
                                 title={
                                    <Text
                                       delete={todo.done}
                                       style={{
                                          opacity: !user ? 0.5 : 1,
                                          color: todo.done ? '#999' : undefined
                                       }}
                                    >
                                       {todo.title}
                                    </Text>
                                 }
                              />
                           </List.Item>
                        )}
                     />
                  </div>

                  {/* Add New Todo */}
                  <Divider />
                  <fetcher.Form method="post">
                     <input type="hidden" name="action" value="add" />
                     <Space.Compact style={{ width: '100%' }}>
                        <Input
                           name="title"
                           placeholder={user ? "添加新的待办事项..." : "需要登录才能添加"}
                           disabled={!user}
                           maxLength={100}
                        />
                        <Button
                           type="primary"
                           htmlType="submit"
                           disabled={!user}
                           icon={<PlusOutlined />}
                        >
                           添加
                        </Button>
                     </Space.Compact>
                  </fetcher.Form>
               </Space>
            </Card>
            {/* Navigation & User Info */}
            <Card>
               <Space direction="vertical" size="middle" className="w-full text-center">
                  <Space size="large" wrap>
                     <Button type="link" href="/about">
                        关于我们
                     </Button>
                     <Button type="link" href="/admin">
                        管理后台
                     </Button>
                  </Space>

                  <Divider />

                  {user ? (
                     <Space>
                        <Avatar icon={<UserOutlined />} size="small" />
                        <Text type="secondary">
                           已登录: <Text strong>{user.email}</Text>
                        </Text>
                     </Space>
                  ) : (
                     <Button
                        type="primary"
                        icon={<LoginOutlined />}
                        href="/admin/auth/login"
                     >
                        登录
                     </Button>
                  )}
               </Space>
            </Card>
         </div>
      </div>
   );
}

// 定义操作类型
type TodoAction = "add" | "update" | "delete";

// 定义需要认证的操作
const AUTHENTICATED_ACTIONS: TodoAction[] = ["add", "update"];

// 验证输入数据
const validateTodoData = (action: TodoAction, formData: FormData) => {
   switch (action) {
      case "add": {
         const title = formData.get("title") as string;
         if (!title || title.trim().length === 0) {
            throw new Response("标题不能为空", { status: 400 });
         }
         if (title.length > 100) {
            throw new Response("标题长度不能超过100个字符", { status: 400 });
         }
         return { title: title.trim() };
      }
      case "update": {
         const id = Number(formData.get("id"));
         const done = formData.get("done") === "on";
         if (!id || id <= 0) {
            throw new Response("无效的ID", { status: 400 });
         }
         return { id, done };
      }
      case "delete": {
         const id = Number(formData.get("id"));
         if (!id || id <= 0) {
            throw new Response("无效的ID", { status: 400 });
         }
         return { id };
      }
      default:
         throw new Response("无效的操作", { status: 400 });
   }
};

export const action = async (args: ActionFunctionArgs) => {
   try {
      const formData = await args.request.formData();
      const action = formData.get("action") as TodoAction;

      if (!action) {
         throw new Response("缺少操作类型", { status: 400 });
      }

      // 验证输入数据
      const validatedData = validateTodoData(action, formData);

      // 根据操作类型决定是否需要认证
      const needsAuth = AUTHENTICATED_ACTIONS.includes(action);
      const api = needsAuth
         ? await getApi(args, { verify: true })
         : await getApi();

      // 执行相应操作
      switch (action) {
         case "add": {
            const { title } = validatedData as { title: string };
            const result = await api.data.createOne("todos", { title, done: false });
            return new Response(JSON.stringify({
               success: true,
               message: "添加成功",
               data: result
            }), {
               status: 200,
               headers: { "Content-Type": "application/json" }
            });
         }
         case "update": {
            const { id, done } = validatedData as { id: number; done: boolean };
            await api.data.updateOne("todos", id, { done });
            return new Response(JSON.stringify({
               success: true,
               message: "更新成功"
            }), {
               status: 200,
               headers: { "Content-Type": "application/json" }
            });
         }
         case "delete": {
            const { id } = validatedData as { id: number };
            await api.data.deleteOne("todos", id);
            return new Response(JSON.stringify({
               success: true,
               message: "删除成功"
            }), {
               status: 200,
               headers: { "Content-Type": "application/json" }
            });
         }
      }
   } catch (error) {
      // 处理认证错误
      if (error instanceof Error && error.message.includes("auth")) {
         return new Response(JSON.stringify({
            success: false,
            message: "需要登录才能执行此操作",
            code: "UNAUTHORIZED"
         }), {
            status: 401,
            headers: { "Content-Type": "application/json" }
         });
      }

      // 处理其他错误
      if (error instanceof Response) {
         return error;
      }

      console.error("Action error:", error);
      return new Response(JSON.stringify({
         success: false,
         message: "操作失败，请稍后重试"
      }), {
         status: 500,
         headers: { "Content-Type": "application/json" }
      });
   }
};
