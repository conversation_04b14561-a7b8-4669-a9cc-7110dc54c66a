{"name": "rrbk1", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/fs-routes": "^7.3.0", "@react-router/node": "^7.3.0", "@react-router/serve": "^7.3.0", "bknd": "0.16.0", "isbot": "^5.1.17", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.3.0"}, "devDependencies": {"@react-router/dev": "^7.3.0", "@tailwindcss/vite": "^4.0.0", "@types/node": "^20", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "react-router-devtools": "^1.1.0", "tailwindcss": "^4.0.0", "typescript": "^5.7.2", "vite": "^5.4.11", "vite-tsconfig-paths": "^5.1.4"}}